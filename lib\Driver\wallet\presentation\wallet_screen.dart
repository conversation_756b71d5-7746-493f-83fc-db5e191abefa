import 'package:flutter/material.dart';
import 'package:sepesha_app/Driver/model/user_model.dart';
import 'package:sepesha_app/Driver/wallet/data/wallet_repository.dart';
import 'package:provider/provider.dart';
import 'package:sepesha_app/l10n/app_localizations.dart';
import 'package:sepesha_app/models/payment_method.dart';
import 'package:sepesha_app/provider/payment_provider.dart';
import 'package:sepesha_app/Utilities/app_color.dart';
import 'package:sepesha_app/Utilities/app_text_style.dart';
import 'package:sepesha_app/widgets/payment_method_card.dart';
import 'package:flip_card/flip_card.dart';
import 'package:sepesha_app/services/session_manager.dart';
import 'package:sepesha_app/models/wallet_transaction.dart';
import 'package:sepesha_app/widgets/wallet_transaction_card.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  bool _isCardFlipped = false;
  List<WalletTransaction> _recentTransactions = [];

  @override
  void initState() {
    super.initState();
    // Refresh wallet balance when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<PaymentProvider>(context, listen: false);
      provider.initialize();
      provider.refreshWalletBalance();
      _loadRecentTransactions();
    });
  }

    void _loadRecentTransactions() {
      // Mock data for now - to replaced with actual API call
      _recentTransactions = [
        WalletTransaction(
          id: '1',
          type: 'credit',
          amount: 15000,
          currency: 'TZS',
          description: 'Ride Payment',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
          status: TransactionStatus.completed,
        ),
        WalletTransaction(
          id: '2',
          type: 'debit',
          amount: 50000,
          currency: 'TZS',
          description: 'Withdrawal',
          timestamp: DateTime.now().subtract(const Duration(days: 1)),
          status: TransactionStatus.completed,
        ),
        WalletTransaction(
          id: '3',
          type: 'credit',
          amount: 8500,
          currency: 'TZS',
          description: 'Ride Payment',
          timestamp: DateTime.now().subtract(const Duration(days: 2)),
          status: TransactionStatus.completed,
        ),
      ];
      setState(() {});
    }

    bool _isDriver() {
      final user = SessionManager.instance.user;
      return user?.userType?.toLowerCase() == 'driver';
    }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(localizations!.paymentMethods),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<PaymentProvider>(
                context,
                listen: false,
              ).refreshWalletBalance();
            },
          ),
        ],
      ),
      body: Consumer<PaymentProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _isDriver() 
                    ? _buildWalletBalanceCard(provider)
                    : _buildSimpleFrontCard(provider),
                const SizedBox(height: 20),
                if (_isDriver()) ...[
                  _buildActionButtons(provider),
                  const SizedBox(height: 16),
                ],
                _buildCompactPaymentMethodsSection(provider),
                const SizedBox(height: 16),
                _buildRecentTransactionsSection(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildWalletBalanceCard(PaymentProvider provider) {
    return FlipCard(
      direction: FlipDirection.HORIZONTAL,
      speed: 1000,
      onFlipDone: (status) {
        setState(() {
          _isCardFlipped = status;
        });
      },
      front: _buildFrontCard(provider),
      back: _buildBackCard(provider),
    );
  }

  Widget _buildFrontCard(PaymentProvider provider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [AppColor.redGrey, AppColor.primary.withOpacity(0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Available Balance',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColor.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Icon(Icons.flip,color: AppColor.white.withOpacity(0.7), size: 20),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.getFormattedWalletBalance('TZS'),
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColor.white,
              ),
            ),
            const Spacer(),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debt',
                      style: TextStyle(fontSize: 12, color: AppColor.white.withOpacity(0.7),
),
                    ),
                    Text(
                      'TZS 0.00', // Replace with actual debt amount
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColor.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackCard(PaymentProvider provider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [AppColor.greyColor, AppColor.secondary.withOpacity(0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Online Payments',
                  style: TextStyle(
                    fontSize: 16,
                    color: AppColor.white.withOpacity(0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Icon(Icons.flip, color: AppColor.white.withOpacity(0.7), size: 20),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.getFormattedWalletBalance('TZS'), // Replace with online payment balance
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColor.white,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Available for withdrawal',
              style: TextStyle(fontSize: 14, color: AppColor.white.withOpacity(0.7)),
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showWithdrawDialog(),
                icon: const Icon(Icons.account_balance_wallet, size: 18),
                label: const Text('Withdraw Funds'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.white,
                  foregroundColor: AppColor.secondary,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleFrontCard(PaymentProvider provider) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        height: 200,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [AppColor.redGrey, AppColor.primary.withOpacity(0.7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Available Balance',
              style: TextStyle(
                fontSize: 16,
                color: AppColor.white.withOpacity(0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              provider.getFormattedWalletBalance('TZS'),
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColor.white,
              ),
            ),
            const Spacer()
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(PaymentProvider provider) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () {
              _showAddFundsDialog();
            },
            icon: const Icon(Icons.add),
            label: const Text('Pay Commission'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColor.primary,
              foregroundColor: AppColor.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCompactPaymentMethodsSection(PaymentProvider provider) {
    if (provider.availablePaymentMethods.isEmpty) {
      return const SizedBox.shrink();
    }

    final selectedMethod = provider.selectedPaymentMethod;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppColor.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColor.grey.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Text(
            'Payment Method',
            style: AppTextStyle.paragraph1(AppColor.blackText).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (selectedMethod != null) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: selectedMethod.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                selectedMethod.icon,
                color: selectedMethod.color,
                size: 20,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              selectedMethod.name,
              style: AppTextStyle.paragraph1(selectedMethod.color).copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ] else
            Text(
              'None selected',
              style: AppTextStyle.paragraph1(AppColor.grey),
            ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: () => _showPaymentMethodsBottomSheet(provider),
            child: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColor.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsSection(PaymentProvider provider) {
      if (provider.availablePaymentMethods.isEmpty) {
      return Expanded(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Payment Methods',
                style: AppTextStyle.heading3(AppColor.blackText),
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Center(
                child: Text(
                  'No payment methods available',
                  style: AppTextStyle.paragraph1(AppColor.grey),
                ),
              ),
            ),
          ],
        ),
      );
    }
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Payment Methods',
              style: AppTextStyle.heading3(AppColor.blackText),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: provider.availablePaymentMethods.length,
              itemBuilder: (context, index) {
                final paymentMethod = provider.availablePaymentMethods[index];
                final isSelected = provider.selectedPaymentMethod?.id == paymentMethod.id;

                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: PaymentMethodCard(
                    paymentMethod: paymentMethod,
                    isSelected: isSelected,
                    walletBalance: paymentMethod.type == PaymentMethodType.wallet
                        ? provider.getFormattedWalletBalance()
                        : null,
                    onTap: () => _onPaymentMethodSelected(paymentMethod, provider),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactionsSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Recent Transactions',
              style: AppTextStyle.heading3(AppColor.blackText),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: _recentTransactions.isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.receipt_long,
                          size: 48,
                          color: AppColor.grey.withOpacity(0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No recent transactions',
                          style: AppTextStyle.paragraph1(AppColor.grey),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _recentTransactions.length,
                    itemBuilder: (context, index) {
                      return WalletTransactionCard(
                        transaction: _recentTransactions[index],
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
  void _showPaymentMethodsBottomSheet(PaymentProvider provider) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Payment Method',
              style: AppTextStyle.heading3(AppColor.blackText),
            ),
            const SizedBox(height: 16),
            ...provider.availablePaymentMethods.map((method) {
              final isSelected = provider.selectedPaymentMethod?.id == method.id;
              return ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: method.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(method.icon, color: method.color, size: 20),
                ),
                title: Text(method.name),
                trailing: isSelected 
                    ? Icon(Icons.check_circle, color: method.color)
                    : null,
                onTap: () {
                  _onPaymentMethodSelected(method, provider);
                  Navigator.pop(context);
                },
              );
            }).toList(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _onPaymentMethodSelected(PaymentMethod method, PaymentProvider provider) async {
    try {
      await provider.selectPaymentMethod(method);
      if (mounted && provider.errorMessage == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment method updated to ${method.name}'),
            backgroundColor: AppColor.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update payment method'),
            backgroundColor: AppColor.primary,
          ),
        );
      }
    }
  }

  void _showAddFundsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Add Funds'),
            content: const Text(
              'This feature will integrate with mobile money and card payments.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Add Funds feature coming soon! Backend integration required.'),
                      backgroundColor: AppColor.warningColor,
                    ),
                  );
                },
                child: const Text('Continue'),
              ),
            ],
          ),
    );
  }

  void _showWithdrawDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Withdraw Funds'),
            content: const Text(
              'Withdraw funds to your mobile money or bank account.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Withdraw feature coming soon! Backend integration required.'),
                      backgroundColor: AppColor.warningColor,
                    ),
                  );
                },
                child: const Text('Continue'),
              ),
            ],
          ),
    );
  }
}
